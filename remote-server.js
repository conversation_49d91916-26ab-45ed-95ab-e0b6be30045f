const https = require('https');
const http = require('http');
const vm = require('vm');
const path = require('path');
const fs = require('fs-extra');

class RemoteServerLoader {
    constructor() {
        this.serverContext = null;
        this.serverCode = null;
        this.isRunning = false;
    }

    // تحميل الكود من URL
    async downloadCode(url) {
        return new Promise((resolve, reject) => {
            const client = url.startsWith('https:') ? https : http;
            
            console.log('🔄 Downloading server code from:', url);
            
            client.get(url, (res) => {
                if (res.statusCode !== 200) {
                    reject(new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`));
                    return;
                }

                let code = '';
                res.on('data', (chunk) => {
                    code += chunk;
                });

                res.on('end', () => {
                    console.log('✅ Server code downloaded successfully');
                    resolve(code);
                });

                res.on('error', (err) => {
                    reject(err);
                });
            }).on('error', (err) => {
                reject(err);
            });
        });
    }

    // تحميل gift-service من URL
    async downloadGiftService() {
        try {
            const giftServiceUrl = 'https://streamtok-c6830.web.app/gift-service.js';
            const giftServiceCode = await this.downloadCode(giftServiceUrl);
            
            // إنشاء module للـ gift-service
            const giftModule = { exports: {} };
            const giftScript = new vm.Script(giftServiceCode, { filename: 'gift-service.js' });
            const giftContext = vm.createContext({
                module: giftModule,
                exports: giftModule.exports,
                require: require,
                console: console,
                process: process,
                __dirname: process.cwd(),
                __filename: path.join(process.cwd(), 'gift-service.js')
            });
            
            giftScript.runInContext(giftContext);
            return giftModule.exports;
        } catch (error) {
            console.warn('⚠️ Could not load gift-service:', error.message);
            return {};
        }
    }

    // إنشاء سياق VM آمن مع جميع المكتبات
    async createSecureContext() {
        console.log('🔧 Creating secure VM context...');

        // تحميل gift-service
        const giftService = await this.downloadGiftService();

        // تحميل p-queue باستخدام dynamic import
        let PQueue;
        try {
            const pQueueModule = await import('p-queue');
            PQueue = pQueueModule.default;
        } catch (error) {
            console.warn('⚠️ Failed to load p-queue, using fallback:', error.message);
            // Fallback: إنشاء كلاس بسيط
            PQueue = class {
                constructor() {
                    this.pending = 0;
                    this.size = 0;
                }
                async add(fn) {
                    this.pending++;
                    try {
                        const result = await fn();
                        return result;
                    } finally {
                        this.pending--;
                    }
                }
            };
        }

        // تحميل جميع المكتبات المطلوبة
        const requiredModules = {
            // Core modules
            'fs': require('fs'),
            'fs-extra': require('fs-extra'),
            'path': require('path'),
            'http': require('http'),
            'https': require('https'),
            'os': require('os'),
            'child_process': require('child_process'),
            'util': require('util'),
            'events': require('events'),
            'stream': require('stream'),
            'url': require('url'),
            'querystring': require('querystring'),
            'crypto': require('crypto'),

            // Express and web
            'express': require('express'),
            'cors': require('cors'),
            'body-parser': require('body-parser'),
            'multer': require('multer'),

            // Socket.IO
            'socket.io': require('socket.io'),
            'socket.io-client': require('socket.io-client'),

            // TikTok and external APIs
            'tiktok-live-connector': require('tiktok-live-connector'),
            'axios': require('axios'),

            // Audio and media
            'node-edge-tts': require('node-edge-tts'),
            'play-sound': require('play-sound'),

            // Utilities
            'uuid': require('uuid'),
            'dotenv': require('dotenv'),
            'p-queue': PQueue,

            // RobotJS (مع معالجة خاصة)
            '@jitsi/robotjs': this.loadRobotJS(),

            // Gift service
            './gift-service': giftService
        };

        // إنشاء دالة require مخصصة
        const customRequire = (moduleName) => {
            if (requiredModules[moduleName]) {
                return requiredModules[moduleName];
            }
            
            // محاولة تحميل المكتبة بشكل عادي
            try {
                return require(moduleName);
            } catch (error) {
                console.warn(`⚠️ Could not load module: ${moduleName}`, error.message);
                return null;
            }
        };

        // إنشاء السياق
        const context = {
            // Node.js globals
            require: customRequire,
            module: { exports: {} },
            exports: {},
            __dirname: process.cwd(),
            __filename: path.join(process.cwd(), 'server.js'),
            process: process,
            console: console,
            Buffer: Buffer,
            global: {},
            setTimeout: setTimeout,
            clearTimeout: clearTimeout,
            setInterval: setInterval,
            clearInterval: clearInterval,
            setImmediate: setImmediate,
            clearImmediate: clearImmediate,

            // إضافة المكتبات مباشرة للسياق
            ...requiredModules
        };

        // ربط global بالسياق نفسه
        context.global = context;

        return vm.createContext(context);
    }

    // تحميل RobotJS مع معالجة الأخطاء
    loadRobotJS() {
        try {
            const robot = require('@jitsi/robotjs');
            robot.setKeyboardDelay(1);
            console.log('✅ RobotJS loaded successfully');
            return robot;
        } catch (error) {
            console.warn('⚠️ RobotJS failed to load:', error.message);
            // إرجاع mock object لتجنب الأخطاء
            return {
                keyTap: () => console.warn('RobotJS not available'),
                typeString: () => console.warn('RobotJS not available'),
                setKeyboardDelay: () => {},
                getMousePos: () => ({ x: 0, y: 0 }),
                moveMouse: () => console.warn('RobotJS not available')
            };
        }
    }

    // تشغيل الكود في VM
    async runServerCode(code) {
        try {
            console.log('🚀 Running server code in VM...');

            // إنشاء السياق الآمن
            this.serverContext = await this.createSecureContext();

            // تشغيل الكود
            const script = new vm.Script(code, {
                filename: 'server.js',
                timeout: 30000 // 30 ثانية timeout
            });

            // تشغيل الكود في السياق
            script.runInContext(this.serverContext);

            this.isRunning = true;
            console.log('✅ Server is running in VM context');
            return true;

        } catch (error) {
            console.error('❌ Error running server code:', error);
            throw error;
        }
    }

    // تحميل وتشغيل الخادم
    async loadAndRunServer(serverUrl) {
        try {
            console.log('🎯 Starting remote server loading...');

            // تحميل الكود
            this.serverCode = await this.downloadCode(serverUrl);

            // تشغيل الكود
            await this.runServerCode(this.serverCode);

            console.log('🎉 Remote server loaded and running successfully!');
            return true;

        } catch (error) {
            console.error('❌ Failed to load remote server:', error);
            throw error;
        }
    }

    // إيقاف الخادم وتنظيف الذاكرة
    cleanup() {
        if (this.serverContext) {
            // تنظيف السياق
            this.serverContext = null;
            this.serverCode = null;
            this.isRunning = false;
            console.log('🧹 Server context cleaned up');
        }
    }

    // التحقق من حالة الخادم
    isServerRunning() {
        return this.isRunning;
    }
}

module.exports = RemoteServerLoader;
