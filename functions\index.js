// تحميل متغيرات البيئة
require('dotenv').config();

const functions = require('firebase-functions');
const express = require('express');
const cors = require('cors');
const http = require('http');
const socketIo = require('socket.io');
const { Server } = require('socket.io');
const { WebcastPushConnection } = require('tiktok-live-connector');
const { fetchGiftsList } = require('./gift-service');
const fs = require('fs-extra');
const path = require('path');
const axios = require('axios');
const { exec } = require('child_process');
const { v4: uuidv4 } = require('uuid');
const os = require('os');
const { EdgeTTS } = require('node-edge-tts');
const multer = require('multer');

// إنشاء تطبيق Express
const app = express();

// إعداد CORS
app.use(cors({ origin: true }));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// إعداد multer لرفع الملفات
const upload = multer({
  dest: '/tmp/uploads/',
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
});

// متغيرات عامة
let connectedUsers = new Map();
let activeSessions = new Map();
let tiktokConnection = null;
let currentProfile = null;

// Firebase Configuration API
app.get('/api/firebase-config', (req, res) => {
  const config = {
    apiKey: process.env.FIREBASE_API_KEY,
    authDomain: process.env.FIREBASE_AUTH_DOMAIN,
    projectId: process.env.FIREBASE_PROJECT_ID,
    storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
    messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID,
    appId: process.env.FIREBASE_APP_ID
  };
  res.json(config);
});

// PayPal Configuration API
app.get('/api/paypal-config', (req, res) => {
  res.json({
    clientId: process.env.PAYPAL_CLIENT_ID,
    environment: process.env.PAYPAL_ENVIRONMENT || 'sandbox'
  });
});

// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({
    message: 'StreamTok Firebase Functions working!',
    timestamp: new Date().toISOString()
  });
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    uptime: process.uptime(),
    memory: process.memoryUsage()
  });
});

// تصدير الدالة
exports.api = functions.https.onRequest(app);
