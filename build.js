
const JavaScriptObfuscator = require('javascript-obfuscator');
const fs = require('fs-extra');
const path = require('path');

// إعدادات التشويش
const obfuscationOptions = {
    compact: true,
    controlFlowFlattening: true,
    controlFlowFlatteningThreshold: 0.75,
    deadCodeInjection: true,
    deadCodeInjectionThreshold: 0.4,
    debugProtection: true,
    debugProtectionInterval: 4000,
    disableConsoleOutput: true,
    identifierNamesGenerator: 'hexadecimal',
    log: false,
    numbersToExpressions: true,
    renameGlobals: false,
    selfDefending: true,
    simplify: true,
    splitStrings: true,
    splitStringsChunkLength: 10,
    stringArray: true,
    stringArrayEncoding: ['base64'],
    stringArrayIndexShift: true,
    stringArrayRotate: true,
    stringArrayShuffle: true,
    stringArrayWrappersCount: 2,
    stringArrayWrappersChainedCalls: true,
    stringArrayWrappersParametersMaxCount: 4,
    stringArrayWrappersType: 'function',
    stringArrayThreshold: 0.75,
    transformObjectKeys: true,
    unicodeEscapeSequence: false
};

// قائمة الملفات المراد تشويشها
const filesToObfuscate = [
    'index.js',
    'public/js/auth-guard.js',
    'public/js/firebase-config.js'
];

// مجلد المخرجات
const outputDir = path.join(__dirname, 'dist');

async function build() {
    try {
        console.log('Starting build process...');

        // 1. تنظيف مجلد المخرجات
        await fs.emptyDir(outputDir);
        console.log('Output directory cleaned.');

        // 2. تشويش الملفات
        for (const file of filesToObfuscate) {
            const filePath = path.join(__dirname, file);
            const fileContent = await fs.readFile(filePath, 'utf8');

            console.log(`Obfuscating ${file}...`);
            const obfuscationResult = JavaScriptObfuscator.obfuscate(fileContent, obfuscationOptions);
            const obfuscatedCode = obfuscationResult.getObfuscatedCode();

            const outputPath = path.join(outputDir, file);
            await fs.ensureDir(path.dirname(outputPath));
            await fs.writeFile(outputPath, obfuscatedCode, 'utf8');
            console.log(`Successfully obfuscated and saved to ${outputPath}`);
        }

        // 3. نسخ الملفات الأخرى الضرورية
        console.log('Copying other necessary files...');
        const filesToCopy = [
            'package.json',
            'electron-preload.js',
            'public', // نسخ المجلد العام بأكمله
            'profiles',
            'translations',
            'speakText-edge-tts.js',
            'last_username.json',
            'icon.ico',
            'gSendInput.ico',
            'firebase.json',
            '.firebaserc'
        ];

        for (const fileOrDir of filesToCopy) {
            const sourcePath = path.join(__dirname, fileOrDir);
            const destPath = path.join(outputDir, fileOrDir);
            if (await fs.pathExists(sourcePath)) {
                await fs.copy(sourcePath, destPath);
                console.log(`Copied ${fileOrDir} to dist.`);
            }
        }

        // 4. نسخ الملفات الجديدة للنظام السحابي
        const remoteFiles = [
            'electron-main-remote.js',
            'remote-server.js',
            'loading.html'
        ];

        for (const file of remoteFiles) {
            const sourcePath = path.join(__dirname, file);
            if (await fs.pathExists(sourcePath)) {
                // نسخ electron-main-remote.js كـ electron-main.js
                const destName = file === 'electron-main-remote.js' ? 'electron-main.js' : file;
                const destPath = path.join(outputDir, destName);
                await fs.copy(sourcePath, destPath);
                console.log(`Copied ${file} as ${destName} to dist.`);
            }
        }

        console.log('Build process completed successfully!');

    } catch (error) {
        console.error('Error during build process:', error);
        process.exit(1);
    }
}

build();
