const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const RemoteServerLoader = require('./remote-server');

let mainWindow;
let loadingWindow;
let serverLoader;

// إعدادات التطبيق
const isDev = process.argv.includes('--dev');

/**
 * إنشاء شاشة التحميل
 */
function createLoadingWindow() {
  loadingWindow = new BrowserWindow({
    width: 500,
    height: 400,
    frame: false,
    alwaysOnTop: true,
    transparent: true,
    resizable: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true
    },
    show: false
  });

  loadingWindow.loadFile('loading.html');

  loadingWindow.once('ready-to-show', () => {
    loadingWindow.show();
    loadingWindow.center();
  });

  loadingWindow.on('closed', () => {
    loadingWindow = null;
  });

  return loadingWindow;
}

/**
 * إنشاء النافذة الرئيسية
 */
function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    center: true,
    show: false,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true
    },
    titleBarStyle: 'default',
    autoHideMenuBar: !isDev,
    backgroundColor: '#f8f9fa'
  });

  // تحميل الواجهة من Firebase Hosting
  mainWindow.loadURL('https://streamtok-c6830.web.app');

  mainWindow.once('ready-to-show', () => {
    // إخفاء شاشة التحميل وإظهار النافذة الرئيسية
    if (loadingWindow && !loadingWindow.isDestroyed()) {
      loadingWindow.close();
    }
    mainWindow.show();
    mainWindow.maximize();
  });

  // تنظيف عند إغلاق النافذة الرئيسية
  mainWindow.on('closed', () => {
    mainWindow = null;
    if (serverLoader) {
      serverLoader.cleanup();
    }
  });

  // فتح أدوات المطور في وضع التطوير
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  // منع التنقل خارج التطبيق
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const allowedDomains = [
      'https://streamtok-c6830.web.app',
      'https://streamtok-c6830.firebaseapp.com'
    ];
    
    const isAllowed = allowedDomains.some(domain => 
      navigationUrl.startsWith(domain)
    );
    
    if (!isAllowed) {
      event.preventDefault();
    }
  });

  // معالجة الروابط الخارجية
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  return mainWindow;
}

/**
 * تحميل وتشغيل الخادم
 */
async function loadServer() {
  try {
    serverLoader = new RemoteServerLoader();
    
    // URL الخادم من Firebase Hosting
    const serverUrl = 'https://streamtok-c6830.web.app/server-index.js';
    
    console.log('🚀 Starting remote server loading...');
    
    // تحميل وتشغيل الخادم
    await serverLoader.loadAndRunServer(serverUrl);
    
    console.log('✅ Server loaded successfully!');
    
    // إنشاء النافذة الرئيسية بعد نجاح تحميل الخادم
    setTimeout(() => {
      createMainWindow();
    }, 2000);
    
  } catch (error) {
    console.error('❌ Failed to load server:', error);
    
    // إظهار dialog للمستخدم
    setTimeout(async () => {
      const result = await dialog.showMessageBox(null, {
        type: 'error',
        title: 'خطأ في تحميل الخادم',
        message: 'فشل في تحميل الخادم من الإنترنت',
        detail: 'يرجى التحقق من الاتصال بالإنترنت والمحاولة مرة أخرى.',
        buttons: ['إعادة المحاولة', 'إغلاق'],
        defaultId: 0
      });
      
      if (result.response === 0) {
        // إعادة المحاولة
        loadServer();
      } else {
        // إغلاق التطبيق
        app.quit();
      }
    }, 3000);
  }
}

/**
 * تهيئة التطبيق
 */
async function initializeApp() {
  try {
    console.log('🚀 Starting StreamTok with remote server loading...');
    
    // إنشاء شاشة التحميل أولاً
    createLoadingWindow();
    
    // انتظار قصير لضمان ظهور شاشة التحميل
    setTimeout(() => {
      loadServer();
    }, 1000);
    
  } catch (error) {
    console.error('❌ Failed to initialize application:', error);
    
    dialog.showErrorBox(
      'خطأ في بدء التطبيق',
      `فشل في تشغيل التطبيق: ${error.message}`
    );
    
    app.quit();
  }
}

/**
 * أحداث التطبيق
 */

// عند جاهزية Electron
app.whenReady().then(initializeApp);

// عند إغلاق جميع النوافذ
app.on('window-all-closed', () => {
  // تنظيف الخادم
  if (serverLoader) {
    serverLoader.cleanup();
  }
  
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// عند تفعيل التطبيق (macOS)
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    initializeApp();
  }
});

// عند إنهاء التطبيق
app.on('before-quit', () => {
  if (serverLoader) {
    serverLoader.cleanup();
  }
});

// معالجة الأخطاء غير المتوقعة
process.on('uncaughtException', (error) => {
  console.error('💥 Uncaught Exception:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
});

module.exports = { mainWindow, loadingWindow, serverLoader };
