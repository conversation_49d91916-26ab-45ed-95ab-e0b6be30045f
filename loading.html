<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StreamTok - جاري التحميل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .loading-container {
            text-align: center;
            color: white;
            max-width: 400px;
            padding: 40px;
        }

        .logo {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: pulse 2s infinite;
        }

        .loading-text {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .progress-container {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #00f5ff, #0080ff);
            border-radius: 10px;
            width: 0%;
            transition: width 0.3s ease;
            animation: shimmer 2s infinite;
        }

        .status-text {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 10px;
            min-height: 20px;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes shimmer {
            0% { background-position: -200px 0; }
            100% { background-position: 200px 0; }
        }

        .error-message {
            color: #ff6b6b;
            background: rgba(255,107,107,0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            display: none;
        }

        .success-message {
            color: #51cf66;
            background: rgba(81,207,102,0.1);
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="logo">🎬 StreamTok</div>
        <div class="loading-text">جاري تحضير التطبيق...</div>
        
        <div class="progress-container">
            <div class="progress-bar" id="progressBar"></div>
        </div>
        
        <div class="status-text" id="statusText">بدء التحميل...</div>
        <div class="spinner" id="spinner"></div>
        
        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>
    </div>

    <script>
        let currentProgress = 0;
        
        // تحديث شريط التقدم
        function updateProgress(percentage, status) {
            const progressBar = document.getElementById('progressBar');
            const statusText = document.getElementById('statusText');
            
            progressBar.style.width = percentage + '%';
            statusText.textContent = status;
            currentProgress = percentage;
        }

        // عرض رسالة خطأ
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            document.getElementById('spinner').style.display = 'none';
        }

        // عرض رسالة نجاح
        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            document.getElementById('spinner').style.display = 'none';
        }

        // محاكاة تقدم التحميل
        function simulateProgress() {
            const steps = [
                { progress: 10, text: 'بدء تحميل الخادم...' },
                { progress: 25, text: 'الاتصال بالخادم...' },
                { progress: 40, text: 'تحميل المكتبات...' },
                { progress: 60, text: 'إعداد بيئة التشغيل...' },
                { progress: 80, text: 'تشغيل الخادم...' },
                { progress: 95, text: 'تهيئة الخدمات...' },
                { progress: 100, text: 'تم تشغيل الخادم بنجاح!' }
            ];
            
            let stepIndex = 0;
            
            function nextStep() {
                if (stepIndex < steps.length) {
                    const step = steps[stepIndex];
                    updateProgress(step.progress, step.text);
                    stepIndex++;
                    
                    if (stepIndex === steps.length) {
                        setTimeout(() => {
                            showSuccess('تم تشغيل الخادم بنجاح! 🎉');
                            setTimeout(() => {
                                window.close();
                            }, 2000);
                        }, 500);
                    } else {
                        setTimeout(nextStep, 1000 + Math.random() * 1000);
                    }
                }
            }
            
            setTimeout(nextStep, 1000);
        }
        
        // بدء المحاكاة
        simulateProgress();
    </script>
</body>
</html>
